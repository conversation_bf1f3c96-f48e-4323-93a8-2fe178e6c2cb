{"name": "ai-editor-standalone", "version": "1.0.0", "description": "独立的AI编辑器组件，基于Novel + TipTap + ProseMirror技术栈", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "export": "next build && next export", "test:ai": "node scripts/test-ai.js", "dev:setup": "./scripts/dev.sh"}, "keywords": ["ai-editor", "rich-text-editor", "novel", "tiptap", "prose<PERSON><PERSON>r", "react", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"@myriaddreamin/typst-ts-renderer": "^0.6.1-rc3", "@myriaddreamin/typst-ts-web-compiler": "^0.6.1-rc3", "@myriaddreamin/typst.ts": "0.6.1-rc3", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@tiptap/core": "^2.26.1", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/react": "2.26.1", "@types/d3": "^7.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "dompurify": "^3.2.6", "framer-motion": "^12.23.12", "import": "^0.0.6", "lucide-react": "^0.487.0", "next": "^15.0.0", "novel": "^1.0.2", "react": ">=16.8.0", "react-dom": ">=16.8.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.4", "remark-gfm": "^4.0.1", "sonner": "^2.0.7", "tailwind-merge": "^3.2.0", "tiptap-markdown": "^0.8.10", "use-debounce": "^10.0.4"}, "devDependencies": {"@next/eslint-plugin-next": "^15.4.6", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/dompurify": "^3.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.45.0", "eslint-config-next": "^15.0.0", "postcss": "^8.4.0", "tailwindcss": "^4.1.11", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ai-editor-standalone.git"}, "bugs": {"url": "https://github.com/your-username/ai-editor-standalone/issues"}, "homepage": "https://github.com/your-username/ai-editor-standalone#readme"}