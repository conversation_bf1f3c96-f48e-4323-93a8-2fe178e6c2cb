# CLAUDE.md

Guidance for Claude Code (claude.ai/code). Always respond in English.

## 1) Purpose & Role
This document defines how to work in this repository: the system’s purpose, key code locations, how to run/test, security expectations, and reply format when proposing changes.

## 2) Project Snapshot
AI Report Generator — converts Word/PDF into structured Markdown + charts, provides SSE-based AI writing assistance, and offers Typst real-time vector compilation with a web preview.

- Backend: Python 3.13, FastAPI, SQLAlchemy, Alembic, MySQL (PyMySQL)
- Frontend: Next.js 15, React 18, TypeScript, TipTap/Novel
- Streaming: SSE (AI writing), WebSocket (Typst real-time compile)
- Typst: typst-ts-cli (vector artifacts .artifact.sir.in), fonts via TYPST_FONT_PATHS
- Tooling: uv (preferred), pnpm, structlog + Rich logging, optional Redis client

## 3) Runbook (dev)
```bash
# Backend
uv install
alembic upgrade head
uv run python main.py          # http://localhost:8000/docs

# Typst prerequisites
uv run python setup_fonts.py   # optional but recommended for CJK
# Ensure the CLI is available in PATH
typst-ts-cli --version

# Frontend
cd web
pnpm install
pnpm dev                       # http://localhost:3000
```

## 4) Repository Map (essentials)
- main.py — FastAPI app, middleware, router mounting
- api/
  - typst_compile_routes.py — HTTP: /api/typst-compile/{health,compile-full,test-conversion}
  - websocket_routes.py — WS: /ws/compile/{session_id} (connection/progress/result)
  - word-format/*, prose/generate/* — document & AI endpoints (selected)
- services/
  - markdown_to_typst_service.py — Markdown → Typst conversion
- core/
  - security.py — JWT validation, OAuth2 bearer, RBAC helpers
  - logging.py — structlog + Rich, SQLAlchemy noise suppression
  - rate_limit.py — sliding-window limiter (used by compile-full)
- database/
  - connection.py — SQLAlchemy session
  - redis_client.py — optional Redis client with PING self-check
- web/src/
  - hooks/useRealtimeCompilation.ts — WS + health orchestration
  - hooks/useCompilationService.ts — frontend service wrapper
  - app/api/auth/* — HttpOnly cookie auth (Next.js server routes)
  - app/api/typst-compile/* — proxy to backend compile endpoints
  - app/components/TypstCoreViewer.tsx — vector preview
  - lib/server-auth.ts — central cookie handling

## 5) Architecture (at a glance)
Word/PDF → parse/extract → AI optimize → Markdown + chart configs →
SSE-assisted editing → WS-triggered compile → Typst vector artifact → web preview/export

Non-blocking policy: Typst compilation runs in a small thread pool; WS loop must not block. Health endpoint reflects executor readiness. Fonts resolved via `fonts/` + `/tmp/typst-fonts` (TYPST_FONT_PATHS).

## 6) Security Expectations (enforced)
- Auth: JWT Bearer for backend; frontend stores tokens in HttpOnly cookies via Next.js server routes.
- RBAC: Use `core/security.py` helpers; do not bypass.
- Rate limits: Sensitive endpoints guarded (e.g., compile-full).
- Uploads: Path traversal and content checks in file handlers.
- Streaming: SSE and WS must handle disconnects; never block event loop.
- Secrets: Never hardcode; use `.env` and `config.py` (pydantic settings).

Changes touching auth, uploads, SSE/WS, or DB must call out risks, validation steps, and rollback.

## 7) Response Contract (how to reply)
- Plan: 3–7 concise steps (what/why).
- Diffs: unified diffs per file; small, focused hunks.
- Commands: exact shell lines to run (backend/frontend/migrations).
- Verification: steps + expected results (incl. WS/SSE and auth flows).
- Rollback: e.g., `alembic downgrade -1` or route toggle.
Keep replies succinct; prefer diffs over prose.

## 8) Coding Conventions & Guardrails
- Python 3.13 compatibility; prefer `uv run`.
- FastAPI: DI-first; typed Pydantic models; explicit error handling.
- SQLAlchemy: avoid N+1; do not edit schema without Alembic migration.
- Streaming: do not block event loop; keep timeouts; add retries where valuable.
- Frontend: small typed components; no client-side secret storage.
- Typst: assume `typst-ts-cli` available; set `TYPST_FONT_PATHS` or use service defaults.
- Do not widen CORS, weaken rate limits, or bypass RBAC.

## 9) Test Guidance (what “done” looks like)
- Backend smoke: server runs; `/api/typst-compile/health` returns healthy/initializing.
- WS path: connect → `connection_established`; send ping → `pong`; no blocking in handlers.
- Compile path: with auth + rate-limit respected, returns vector bytes; errors are structured.
- Frontend: editor receives progress → complete; viewer renders without layout shifts.
