# 🚀 AI智能报告生成系统

AI 驱动的文档处理与实时编译预览平台：Word/PDF → Markdown/图表 → Typst 向量/PDF，配合 AI 写作助手与前端预览。

## 📋 项目概述

本系统提供端到端的文档处理与报告生成能力：从上传、解析、结构化与图表生成，到 Typst 高质量排版与前端实时预览。最新引入基于 WebSocket 的 Typst 实时编译链路，让编辑器获得接近 IDE 的“所见即所得”体验。

### 🎯 核心功能
- 文档处理：Word/PDF 解析（结构/表格/图表/图片），图表元数据抽取与 D3 配置生成。
- AI 写作助手：上下文增强的流式写作与润色（SSE），无阻塞前端体验。
- Typst 集成：Markdown → Typst → 向量工件（`.artifact.sir.in`，typst-ts-cli），稳定预览；可扩展到 PDF 导出。
- 实时编译：WebSocket 驱动的实时编译反馈（连接确认/进度/完成/错误），健康检查与超时保护。
- 安全与配置：后端 JWT/Bearer；前端 Next.js 服务端 API 通过 HttpOnly Cookie 托管令牌；Redis 可选接入与 PING 自检。

## 🏗️ 架构一览

```
上传 Word/PDF → 解析与抽取 → AI 优化 → Markdown + 图表配置 →
实时编辑（SSE/AI）→ WebSocket 触发编译 → Markdown→Typst→Vector → 前端预览/导出
```

```
Frontend (Next.js 15, React 18, TS)
  ├─ AI Editor（SSE）
  ├─ TypstCoreViewer（向量预览）
  ├─ useRealtimeCompilation / useCompilationService（WS + 健康检查）
  └─ API routes: /api/auth/*, /api/typst-compile/*

Backend (FastAPI, Python 3.13)
  ├─ /api/typst-compile/*（HTTP）
  ├─ /ws/compile/{session_id}（WebSocket）
  ├─ services/markdown_to_typst_service.py
  ├─ core/logging.py（结构化日志 + 降噪）
  └─ database/redis_client.py（可选）

Typst toolchain
  ├─ typst-ts-cli（向量工件 .artifact.sir.in）
  └─ 字体目录：fonts/ + /tmp/typst-fonts（TYPST_FONT_PATHS）
```

## 🆕 最新更新（2025-08-27）

feat(typst-integration): Typst 实时编译与预览 + WebSocket 稳定性提升 + Redis 接入

- 新增完整编译链路：`/api/typst-compile/{health,compile-full,test-conversion}`（HTTP）+ `/ws/compile/{session}`（WS）。
- 引入 typst-ts-cli，输出向量工件 `.artifact.sir.in`，前端以 SVG/Canvas 稳定预览，禁用动画避免闪烁。
- WebSocket 增强：连接建立确认、服务预热、30s 超时保护、错误恢复与用户提示。
- 结构化日志与降噪：`core/logging.py` 默认减少 SQLAlchemy 噪音；严重错误具备上下文。
- Redis 可选接入：`database/redis_client.py` + `.env.example` 中 `REDIS_*`，带 PING 自检。
- 前端鉴权改为服务端托管 HttpOnly Cookies，降低敏感信息暴露风险。

## 🛠️ 技术栈

### 后端
- Python 3.13、FastAPI、SQLAlchemy、Alembic、MySQL（PyMySQL）
- SSE（AI 写作流式）、WebSocket（Typst 实时编译）
- structlog + Rich 日志、可选 Redis 客户端

### 前端
- Next.js 15、React 18、TypeScript、TipTap/Novel
- TypstCoreViewer（向量预览）、D3.js
- 服务端 API 路由：鉴权与后端代理（HttpOnly Cookies）

### Typst 工具链
- typst-ts-cli（向量工件生成）、官方 Typst CLI（可选 PDF）
- 字体：Noto CJK 等，`setup_fonts.py` 一键安装/验证

## 🚀 快速开始（本地开发）

### 1) 前置要求
- Python 3.13+
- Node.js 24+（建议安装 pnpm）
- MySQL 8.0+
- Redis（可选）
- typst-ts-cli（确保 `typst-ts-cli --version` 可用）

### 2) 克隆与后端安装
```bash
git clone <repository-url>
cd ai-report-gen

# Python 依赖
uv install

# 环境变量
cp .env.example .env

# 数据库迁移
alembic upgrade head

# 启动后端
uv run python main.py  # 打开 http://localhost:8000/docs
```

### 3) Typst 前置准备（强烈建议）
```bash
# CJK 字体与模板（如需中文文档）
uv run python setup_fonts.py

# typst-ts-cli 安装（任选其一，视平台）
# - 参考官方文档安装： https://github.com/Myriad-Dreamin/typst.ts
# - 或使用包管理器（示例）：
#   npm i -g typst-ts-cli
#   pnpm add -g typst-ts-cli

# 验证命令可用
typst-ts-cli --help
```

### 4) 前端安装
```bash
cd web
pnpm install
echo "NEXT_PUBLIC_AI_BACKEND_URL=http://localhost:8000" > .env.local
pnpm dev  # http://localhost:3000
```

### 5) 基础验证
- API 文档: http://localhost:8000/docs
- Typst 健康检查: http://localhost:8000/api/typst-compile/health
- WebSocket 回归脚本: `uv run python scripts/test_websocket_fix.py`

## 🔌 API 摘要

- WebSocket: `GET /ws/compile/{session_id}`
  - 事件：`connection_established` → `compilation_started`/`compilation_progress` → `compilation_complete` | `compilation_error`
  - 说明：连接确认带 `service_ready`；编译超时默认 30s；错误带 `retry_after` 提示。

- HTTP: `/api/typst-compile`
  - `GET /health`：线程池与服务就绪检查。
  - `POST /compile-full`：Markdown → Typst → Vector（需要鉴权 + 限流）。
  - `POST /test-conversion`：仅执行 Markdown → Typst 转换。

## 🔒 认证与安全

- 后端：JWT Bearer（OpenAPI 中 `Authorize` 可测试），`core/security.py` 统一验证。
- 前端：Next.js API 路由托管 HttpOnly Cookies（安全、可控），代理向后端注入 `Authorization`。
- 速率限制：关键接口具有限流依赖；日志采用结构化输出。

## 🧰 目录结构

```
api/                # 路由（含 typst-compile 与 websocket）
services/           # 业务逻辑（markdown_to_typst 等）
core/               # 安全/日志/中间件
database/           # 连接与客户端（含 redis_client.py）
models/             # 数据模型
web/                # Next.js 前端（hooks、components、API routes）
assets/template/    # Typst 模板
fonts/              # 项目字体（与 /tmp/typst-fonts 共同作为搜索路径）
scripts/            # 集成回归脚本（test_websocket_fix.py 等）
```

## 🐛 故障排查（FAQ）

- 找不到 `typst-ts-cli`：确保安装并在 PATH；执行 `typst-ts-cli --version` 验证。
- CJK 字体缺失或乱码：运行 `uv run python setup_fonts.py`，并确认 `TYPST_FONT_PATHS` 指向 `fonts/:/tmp/typst-fonts`。
- 编译超时：检查内容复杂度、CPU 占用与字体可用性；适当减少内容或重试。
- 401/鉴权失败：后端需 Bearer；前端通过 API 路由设置/转发 Cookie，无需本地存储令牌。

---

更多协作与贡献规范，请参考 `AGENTS.md`。 
